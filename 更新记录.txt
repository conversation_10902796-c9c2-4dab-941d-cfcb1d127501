版本历史
v4.646
● 发布时间 2025.08.07
（1）优化算法决策逻辑，取消非计算帧沿用此前计算帧的决策结果，避免出现错误的报警触发帧
（2）优化gb28181接入功能，在SIP注册时，兼容更多的注册信息加密算法
（3）修复添加布控绘制多边形时，多边形顶点超过10个，无法正常计算的问题
（4）新增支持Yolo8-Seg分割算法和Yolo11-Seg分割算法
（5）优化AREA/STAY/LEAVE行为算法，兼容支持分割算法
（6）优化CROSSCOUNT行为算法，兼容支持过滤相同ID引起的重复触发警告
（7）优化日志记录功能 
注：使用升级包升级的最低版本要求是v4.636
v4.644
● 发布时间 2025.07.29
（1）布控扩展参数新增支持可配置ps_effect_min_fps和pull_frequency，通过合理配置pull_frequency可以大幅度提升性能，大幅度降低CPU消耗
（2）布控扩展参数新增支持可配置中文提示词，可以为每一个布控设置独立的大模型分析提示词
（3）API行为算法扩展请求参数
注：使用升级包升级的最低版本要求是v4.636
v4.643
● 发布时间 2025.07.26
（1）后台管理服务首次实现Release模式代替Debug模式，提升性能，提升安全性，减少后台框架Debug模式导致的安全漏洞
（2）后台管理登录页新增支持账号信息公钥加密传输功能，验证码功能，连续登录失败锁定功能
（3）后台管理启动服务新增支持设置HOST白名单
（4）优化TensorRT的推理预处理，优化主要围绕split/resize/convert，subtract/divide，cudaStream复用等
（5）算法框架优化围绕TensorRT推理的Yolo/ResNet/FaceNet/DeepSort等，性能提升超过15%以上
（6）布控管理页面新增快捷设置功能，快捷设置功能对于修改参数，非常高效
（7）优化CROSSCOUNT行为算法，支持配置目标任意位置作为计算中心点，扩展自定义参数user_data
（8）优化SUPER行为算法，支持配置目标任意位置作为计算中心点，扩展自定义参数user_data
（9）优化STAY行为算法，支持配置显示内部目标和触发事件的时长，扩展自定义参数user_data
（10）优化LEAVE行为算法，支持配置显示内部目标和触发事件的时长，扩展自定义参数user_data
（11）人脸特征库初始化功能，优化请求超时导致的报错，优化记录日志的错误
（12）后台管理上报报警数据时新增视频分组字段
（13）后台管理优化请求IP音柱报警参数
（14）完善日志记录功能
注：使用升级包升级的最低版本要求是v4.636
v4.639
● 发布时间 2025.07.14
（1）gb28181接入功能，进一步优化公网NAT网络环境下的兼容能力
（2）高级版多进程环境下，优化人脸识别功能数据同步问题
（3）后台管理，优化视频管理页面显示效果，支持分组和搜索功能
（4）后台管理，优化布控管理页面显示效果，支持分组和搜索功能
（5）后台管理，优化报警管理页面显示效果
注：使用升级包升级的最低版本要求是v4.636
v4.637
● 发布时间 2025.07.08
（1）gb28181接入功能，优化SIP信令TCP模式
（2）gb28181接入功能，优化公网NAT网络环境下的兼容能力，提升接入不同类型摄像头的兼容性
（3）gb28181接入功能，接收视频流功能新增支持接收音频功能
（4）后台管理，优化报警管理页面显示效果
（5）后台管理，修复录像计划播放页面的bug
（6）后台管理，新增支持可设置多进程模式
（7）限制软件子模块服务的对外访问权限，提升安全性
（8）限制打印模型加密的扩展参数，提升安全性
（9）打印日志时，读取某些硬件特有参数时，提前判断对应命令是否存在
注：使用升级包升级的最低版本要求是v4.636
v4.636
● 发布时间 2025.06.28
（1）优化音频解码和音频重采样的格式兼容性
（2）新增支持内置推理引擎，进行语音识别
（3）新增支持可配置接入IP音柱报警
（4）优化日志记录功能
注：此前修改过默认端口的用户，升级v4.636后，需要重新修改端口
注：使用语音识别功能，请参考文档 
v4.635
● 发布时间 2025.06.21
● （1）新增语音识别功能（大规模重构和优化了分析器核心模块）
● （2）优化报警合成功能（关闭合成视频模式下，进一步提升报警推送速度，降低内存消耗）
● （3）优化ResNet分类模型推理后处理
● （4）优化视频分辨率支持范围
注：此前修改过默认端口的用户，升级v4.635后，需要重新修改端口
v4.634
● 发布时间 2025.06.02
● （1）后台管理新增测试基础算法模型的工具
● （2）视频流接入协议新增支持rtsps/rtmps/https等ssl加密的协议
● （3）合成报警图片支持设置画框/不画框/画框+不画框，共计三种类型（不画框类型的报警图片可以直接导出为labelme格式训练样本集）
● （4）新增报警数据查询/删除/清理缓存/导出为样本等开放接口
● （5）后台管理/人脸管理页面扩展下拉框选项数据
注：此前修改过默认端口的用户，升级v4.634后，需要重新修改端口
v4.633
● 发布时间 2025.05.26
● （1）优化检测模式2~5，跳帧解码时，支持丢弃循环滤波和IDCT
● （2）新增检测模式6，全帧解码时，支持丢弃循环滤波和IDCT
● （3）针对arm-rk，arm-jetson，arm-昇腾等arm版本， 部分依赖库开启neon指令集加速
v4.632
● 发布时间 2025.05.19
● （1）后台管理日志新增写入视频流相关操作过程
● （2）导出日志新增导出config.json,settings.json,config.ini
● （3）导出日志新增导出在线视频流，在线布控等数据
● （4）导出日志调整支持可配置导出流媒体日志
● （5）检测版本接口新增上传推理引擎参数等参数
v4.631
● 发布时间 2025.05.12
● （1）新增支持TensorRT推理Yolo8-obb和Yolo11-obb算法模型
● （2）新增支持OpenVINO推理Yolo8-obb和Yolo11-obb算法模型
● （3）rk版新增支持RKNPU推理ResNet算法
● （4）新增支持系统设备ID绑定授权（适用于云服务器等mac地址经常变化的机器）
● （5）后台管理所有模块新增增删改查的info级日志
● （6）后台管理新增开启调试模式的功能
● （7）优化分析器权限校验逻辑
● （8）优化启动器控制逻辑
v4.628
● 发布时间 2025.05.03
● （1）新增业务算法模式六：分类算法->检测算法->行为算法
● （2）新增业务算法模式七：检测算法->分类算法->特征算法->行为算法
● （3）解决gb28181接入摄像头时，摄像头名称中文乱码的显示问题
● （4）新增手动抓拍图片和手动录像的开放接口
● （5）安装包内置新增人脸识别算法v2（支持人脸图片的质量过滤）
v4.627
● 发布时间 2025.04.23
● （1）针对接入视频流方式三，在被动接收推流时，自动新增对应数据 
● （2）新增可配置是否开启强制逐帧发送报警功能
● （3）算法流绘制矩形多边形框时，新增支持自定义颜色/厚度
● （4）算法流绘制线段时，新增支持自定义颜色/厚度
● （5）算法流绘制检测目标时，新增支持自定义颜色/厚度/字体大小
● （6）OSD贴图，新增支持自定义绘制字体厚度
● （7）OSD贴图，优化字体像素拼接算法，提升性能
v4.624
● 发布时间 2025.04.13
● （1）rk版/昇腾版优化解码内存复用技术，减少内存碎片，提升稳定性
● （2）优化拉流重试功能（针对中途中断的视频流，优化重试逻辑）
● （3）优化解码重试功能（针对初始化失败或中途故障的解码器，优化重试逻辑）
● （4）新增记录连续读流或连续解码失败的日志（用于排查问题）
● （5）新增可配置视频解码线程数量
v4.623
● 发布时间 2025.04.10
● （1）布控扩展参数新增支持可配置跳帧解码功能
● （2）区域行为算法新增支持返回目标区域序号
● （3）API行为算法新增支持返回目标区域序号
v4.622
● 发布时间 2025.04.05
● （1）修复删除布控的bug（非正常关闭布控时，会出现状态未及时变更，导致无法删除）
● （2）优化推理计算的调度逻辑
● （3）rk版优化视频解码帧的临时缓存复用
● （4）rk版优化视频解码后像素格式转换性能
● （5）rk版rga库升级至1.10.3
v4.621
● 发布时间 2025.03.31
● （1）优化接入集群管家xcnvs的功能
● （2）修复人脸识别功能的bug
● （3）修复取消布控功能的bug
● （4）优化模块间通信性能
● （5）优化添加布控报错时提示信息
● （6）新增英特尔集显qsv硬解码功能（仅建议多进程自动负载模式的高级版授权使用该功能）
v4.618
● 发布时间 2025.03.22
● （1）优化高级版多进程负载均衡算法，新增自动负载均衡模式（提升性能的同时，大幅度降低使用多进程版本的配置难度）
● （2）授权路数变更为对摄像头的路数控制（对于单个摄像头绑定多个算法的场景，降低购买成本）
v4.617
● 发布时间 2025.03.14
● （1）算法类别全面支持设置中文
● （2）新增支持车牌识别/包装日期/表单/通用卡证识别等OCR相关算法 
● （3）优化API行为算法
v4.616
● 发布时间 2025.03.06
● （1）可支持视频帧率范围调整至[7,60]
● （2）优化导出日志功能（提升通过日志排查问题的能力）
● （3）优化视频解码帧自定义容器
● （4）优化人脸库添加大量人脸数据时容易崩溃的问题
● （5）开放接口全面升级（新增安全校验机制）
● （6）新增添加人脸/删除人脸/查询所有人脸/根据图片查询人脸/人脸算法开启/人脸算法关闭等开放接口
● （7）新增添加录像计划/删除录像计划/查询录像计划/查询录像文件列表/查询录像文件播放地址开放接口
● （8）新增指定编号查询视频流/指定编号查询布控开放接口
v4.615
● 发布时间 2025.02.24
● （1）新增针对视频流的遮挡检测，灰屏检测，花屏检测等内置算法
● （2）人脸识别行为算法，新增支持可配置陌生人报警
● （3）计划任务，新增支持自动开启人脸识别类型
● （4）布控扩展参数，新增支持可配置强制触发报警
● （5）修复开放算法能力接口的bug
v4.614
● 发布时间 2025.02.19
● （1）新增支持视频解码复用，大幅度提升性能（针对相同视频流与多个算法组成的多条布控，在运算时，实现了解码复用技术，可大幅度提升性能）
v4.613
● 发布时间 2025.02.11
● （1）优化业务算法模式二（优化追踪算法的性能和可自定义追踪相关阈值）
● （2）优化授权码验证逻辑（解决因网卡名称变化引起的授权失败问题）
● （3）优化rk版RKNPU推理人脸检测算法（解决上传特殊分辨率的人脸图片时报错问题）
● （4）优化rk版RKNPU推理人脸特征算法（解决人脸库特征无法匹配的问题）
● （5）修复算法开放接口的bug
v4.612
● 发布时间 2025.01.28
● （1）优化分析器出现异常重启时，布控中断的显示问题
● （2）优化使用升级包升级版本时，升级包可以控制旧版本范围，避免选用了不兼容的升级包
v4.611
● 发布时间 2025.01.22
● （1）优化软件授权状态的显示问题。主要用于解决部分服务器级的机器在大量布控时，一会出现授权，一会出现未授权的状态问题
● （2）新增自动定时清理日志和缓存功能
● （3）优化写入日志的埋点，进一步提升通过日志排查错误的能力
● （4）修复启动器自动托管分析进程的bug
v4.610
● 发布时间 2025.01.17
● （1）新增支持导入离线升级包更新版本的功能
● （2）新增支持手动清理日志缓存功能
● （3）优化后台管理主题
v4.609
● 发布时间 2025.01.06
● （1）gb28181协议新增支持接入多通道录像机
● （2）gb28181协议新增支持云台控制，并对外提供云台开放接口
● （3）API行为算法新增支持用户自定义字段
● （4）COUNT行为算法新增支持小于等于阈值的触发报警逻辑
● （5）新增的开放接口需要通过安全验证
v4.608
● 发布时间 2024.12.30
● （1）异常停止的布控可自动恢复执行功能
● （2）执行中的布控，修改关联业务算法的进程序号，将不再影响该布控关闭功能
● （3）公网部署环境下，解决gb28181无法正常接入摄像头的问题
● （4）可支持视频帧率范围调整至[13,60]，可合成报警视频最大时长调整至120秒
● （5）rk版RKNPU升级至2.3.0
● （6）rk版RKNPU新增支持推理XcFaceNet（适用于rk芯片人脸特征提取加速）
v4.607
● 发布时间 2024.12.24
● （1）优化COUNT行为算法（优化阈值判断逻辑）
● （2）优化PLATE行为算法（优化场景逻辑）
● （3）昇腾版推理引擎支持自适应模型分辨率
● （4）rk版推理引擎支持自适应模型分辨率
● （5）报警管理新增支持设置邮件通知
● （6）报警管理新增支持审核功能
● （7）优化后台管理UI
v4.606
● 发布时间 2024.12.13
● （1）优化STAY行为算法（主要用于违停，违规逗留等业务算法，优化后大幅度提升抗误检，抗干扰能力，新增支持自定义事件名称）
● （2）优化LEAVE行为算法（主要用于离岗，无人值守等业务算法，优化后大幅度提升抗误检，抗干扰能力，新增支持自定义事件名称）
● （3）渲染算法流支持用户自定义渲染目标，API行为算法扩展接收返回参数
● （4）优化Xcc硬件加速兼容库
● （5）修复linux系统下使用API行为算法bug（以及取消布控时大概率崩溃问题）
● （6）优化rk版模型推理预处理，进一步降低cpu消耗
v4.604
● 发布时间 2024.11.20
● （1）支持接入xcnvs集群管理平台
● （2）xcnvs集群管理平台，支持查看和管理任意节点的任意摄像头/算法流/录像回放等
● （3）xcnvs集群管理平台，非常适合部署在公网，用于管理分散在各个内网的节点
● （4）修复linux版本的gb28181接入问题
v4.602
● 发布时间 2024.11.11
● （1）基础算法新增XcFaceNet，XcFaceNet主要用于人脸特征提取。用户除了可以使用平台内置的XcFaceNet模型，也可以自己训练模型
● （2）人脸识别功能进一步完善，开始内置人脸模型，任何授权用户均可以免费使用该模型
● （3）追踪算法优化预处理，提升预处理性能和优化追踪准确性
● （4）检测算法优化预处理，提升预处理性能
● （5）分类算法优化预处理，提升预处理性能
● （6）arm架构的版本均首次开始支持OpenVINO-2024.4
● （7）x86架构的版本OpenVINO-2024.4
v4.601
● 发布时间 2024.11.04
● （1）x86版全面提升性能，在编解码，推理预处理，推理全部加速的情况下，进一步降低cpu消耗
● （2）arm 昇腾版在解码->像素格式转换->推理预处理->推理->像素格式转换->编码，全流程采用官方库硬件加速，性能大幅度提升
● （3）arm rk版在解码->像素格式转换->推理预处理->推理->像素格式转换->编码，全流程采用官方库硬件加速，性能大幅度提升
● （4）优化软件启动器
v4.508
● 发布时间 2024.10.31
● （1）rk版优化图像互转加速
● （2）x86版优化yuv与bgr互转算法，性能有小幅度提升
● （3）修复开启多进程时，某个进程崩溃，进程排序引起的bug
● （4）优化布控管理页面，新增显示多进程实时信息/显示布控选用业务算法/显示业务算法所属进程等
● （5）优化摄像头管理页面，新增全部删除功能，添加摄像头页面支持自定义编号
v4.507
● 发布时间 2024.10.25
● （1）修改多进程版本的授权方案，降低多进程版本的售价门槛
● （2）优化多进程版本的使用体验，降低多进程版本的操作难度
● （3）系统内置离岗/无人值守行为算法扩展区域编号字段
v4.506
● 发布时间 2024.10.21
● （1）重构分析器
● （2）修复TensotRT推理引擎的资源释放问题（此前的版本在模型实例释放后，存在轻微内存泄漏问题）
● （3）修复模型推理扩展参数的解析问题
● （4）优化Linux版本的图片编解码性能
● （5）新增人脸识别功能
● （6）报警过滤器上传接口/报警上传接口等包含关键点的接口上传字段均有所调整
v4.505
● 发布时间 2024.10.13
● （1）算法管理模块部分功能更名，更名后分别是基础算法，行为算法，业务算法
● （2）行为算法新增APIv2类型，可以在调用API算法的基础上，同时还能利用系统内置的行为算法
● （3）优化日志记录功能
v4.504
● 发布时间 2024.09.30
● （1）录像数据和报警数据新增支持按照存储空间上限自动覆盖
● （2）解决后台管理sqlite多线程不安全问题
● （3）修复录像计划勾选录音时无法正常录制的bug
● （4）写入本地的报警数据新增一个描述报警结果的json文件
● （5）添加录像计划支持自定义编号
● （6）平台开放接口新增8个，查询软件基本信息/查询存储信息/重启软件/重启系统/添加录像计划/编辑录像计划/删除录像计划/查询录像计划
v4.503
● 发布时间 2024.09.25
● （1）升级模型加密算法，支持对加密的模型写入试用时长和自定义编号，并兼容原有的模型加密算法
● （2）用户上传TensorRT或OpenVINO的模型时，系统会自动判断是否已经加密，对于未提前加密的模型，系统会自动加密，已经提前加密的模型，系统不会重复加密
● （3）OSD绘图新增支持设置算法和FPS显示的起点x坐标和y坐标
● （4）修复开放接口/添加布控接口绘制区域参数的问题
● （5）API行为算法上传接口，报警上传接口，过滤器上传接口均扩展参数
v4.502
● 发布时间 2024.09.20
● （1）报警数据上传新增图片或视频的url字段，可以替代base64，同时可以关闭base64字段的上传
● （2）新增支持h265的web播放器代替此前的h265转码器方案，可以降低机器的计算压力，提升性能
● （3）移除h265转码服务器
● （4）优化NVR存储功能和播放功能
v4.501
● 发布时间 2024.09.14
● （1）gb28181接入新增支持手动开启转发和手动停止转发
● （2）gb28181接入优化sip信令交互的兼容性
● （3）新增支持录像计划功能，可设置录像天数，可支持录像回放
● （4）全面修改软件安装包组件名称，可进一步避免与已有软件重名的问题
v4.439
● 发布时间 2024.09.10
● （1）后台管理新增支持将报警数据存储配置在任意硬盘位置
● （2）后台管理模块新增文件服务，文件服务可指向任意硬盘位置，对外提供HTTP访问
● （3）后台管理优化数据库调用逻辑，优化页面交互逻辑
● （4）新增开放接口，添加摄像头接口+删除摄像头接口
● （5）一键启动安装包修改启动器名称
v4.437
● 发布时间 2024.09.02
● （1）新增OVERLAP重叠后处理行为算法
● （2）新增STAY徘徊逗留后处理行为算法
● （3）修复FIGHT打架后处理行为算法的参数无效问题
● （4）修复COUNT计数后处理行为算法的实时数据接口查询问题
● （5）优化NOONE和LEAVE后处理行为算法的参数重复参数
● （6）修复算法流程模式3和算法模式4的崩溃可能性问题
● （7）修复无法正常播放MJpg流的问题
v4.436
● 发布时间 2024.08.25
● （1）新增支持在Windows平台软件自启动功能
● （2）优化启动时的环境检测执行速度
● （3）针对模式三（检测>>分类>>行为），分类算法新增支持背景分类（可以在目标检测算法的基础上，通过对背景分类，进行背景过滤）
● （4）分类算法修复此前仅能显示ResNet的bug
v4.435
● 发布时间 2024.08.18
● （1）新增支持在算法流和报警视频中，绘制中文名称
● （2）新增支持任务计划功能。用户可以基于任务计划功能，实现对单个或多个布控任务，单个或多个视频流转发任务，单个轮巡任务，设置定时执行，设置指定时间点执行。用户可以基于任务计划功能，实现定时重启软件，定时重启系统。用户可以基于任务计划功能，实现对频繁离线维修的摄像头进行间隔扫描，避免长期离线
● （3）优化后台管理报错提示，出现授权报错时，区分报错细节
● （4）修复同一台设备更换不同系统时，永久授权码不兼容的问题
● （5）优化后台管理UI
v4.434
● 发布时间 2024.08.09
● （1）全面优化行为算法
● （2）新增SUPER行为算法（可以广泛应用在许多算法中，功能非常强大）
● （3）新增打架行为算法
● （4）新增暴力行为算法
● （5）新增目标计数算法
● （6）新增SUPER版越线计数算法
● （7）优化周界入侵行为算法
● （8）优化TensorRT推理引擎推理输出的维度校验问题
● （9）优化计算目标与周界框重叠比例的计算过程
● （10）优化后台管理报警管理页面
● （11）优化后台管理列表数据分页按钮
v4.432
● 发布时间 2024.08.04
● （1）新增支持对布控单独设置视频硬解码和硬编码
● （2）新增支持设置自动清理报警数据功能，可以设置保存天数
● （3）后台管理新增支持设置gb28181相关参数
● （4）rk版新增支持yolo8检测
v4.431
● 发布时间 2024.07.28
● （1）新增支持在后台管理修改软件启动配置
● （2）新增支持设置软件自启动
v4.430
● 发布时间 2024.07.22
● （1）新增支持绘制多目标框，包括绘制多个矩形目标框，多个多边形目标框
● （2）新增支持无人值守算法后处理，离岗检测算法后处理
● （3）新增开放图片检测接口
v4.428
● 发布时间 2024.07.16
● （1）优化兼容算法动态库
● （2）修复算法推流的回帧问题，统一算法推流的编码帧率，进一步提升编码和推流的性能
● （3）优化播放h265编码摄像头的视频质量
● （4）优化算法合成流视频质量
● （5）报警视频管理页面新增筛选条件，包括布控，视频流，算法，日期范围等
● （6）修复合成报警不包含图片时部分情况下图片地址错误的bug
● （7）rk版新增支持mpp视频硬解码加速，预处理支持rga加速
● （8）优化日志输出内容，方便排查问题
● （9）优化报警详情页面
● （10）报警管理新增支持显示不画框的报警数据
v4.422
● 发布时间 2024.06.20
● （1）优化动态库类型行为算法，进一步提升可二次开发能力
● （2）分析器新增兼容算法动态库，所有国产硬件的算法兼容均放在该动态库进行实现
● （3）后台管理基础算法模块，新增支持添加rknn模型，om模型
v4.421
● 发布时间 2024.06.12
● （1）新增支持OnnxRuntime推理引擎
● （2）去除基础算法推理参数的dimension参数，后续版本不再需要填写该参数
● （3）优化动态库类型行为算法，使用全局统一目标检测类型，避免对象拷贝，提升性能
● （4）优化API类型行为算法，解决参数在传递过程中过度的拷贝，提升性能
● （5）优化启动器的调度逻辑
v4.419
● 发布时间 2024.06.06
● （1）修复报警视频弹窗的bug
● （2）修复模块之间安全验证逻辑的bug
● （3）新增内置算法
● （4）修复布控轮巡功能的bug
v4.417
● 发布时间 2024.05.31
● （1）新增支持算法流动态编码功能
● （2）新增在线导出配置/导入配置功能
● （3）新增在线导出日志功能
v4.414
● 发布时间 2024.05.19
● （1）新增算法流程模式，截止当前版本，一共支持5种算法流程模式，分别如下：
● （1-1）模式1：检测算法>>行为算法
● （1-2）模式2：检测算法>>追踪算法>>行为算法
● （1-3）模式3：检测算法>>分类算法>>行为算法
● （1-4）模式4：分类算法>>行为算法 
● （1-5）模式5：行为算法 
● （2）行为算法扩展API功能，原有基础算法的API调用功能合并到行为算法，当选择模式5的时候，可以直接在行为算法API端接收图片等相关数据
v4.413
● 发布时间 2024.05.17
● （1）修复分析器多进程的bug
● （2）新增用户管理模块，区分管理员和普通用户，管理员可以增删改查用户
● （3）自定义配置参数转移至配置文件settings.json，支持修改下载地址，文档地址，logo，作者链接等
● （4）修复此前版本报警过滤器的bug
● （5）统一软件启动图标
● （6）修复分析器合成不包含报警视频的报警时，内存占用过大的问题
v4.411
● 发布时间 2024.05.13
● （1）新增报警过滤器功能
● （2）修复ONVIF功能的bug
● （3）优化机器授权码授权检测逻辑，将所有检测逻辑全部移动至分析器模块
● （4）减少各个子模块频繁检测导致性能浪费
v4.409
● 发布时间 2024.05.07
● （1）新增支持ONVIF搜索功能，ONVIF自动获取地址功能，ONVIF获取设备信息功能，ONVIF截屏功能
● （2）新增支持视频流手动录像功能，手动截图功能
● （3）新增支持可自定义配置存储根路径，已将报警数据移至该路径，后续录像截屏数据均会放在该路径
● （4）优化报警数据存储结构，扩展报警描述文本信息
● （5）优化后台管理依赖库，去除未使用依赖库，提高web访问性能
v4.408
● 发布时间 2024.05.01
● （1）新增支持检测算法>>分类算法>>行为算法的业务模式
● （2）新增支持OpenVINO推理追踪算法
● （3）优化原有TensorRT推理追踪算法的计算性能
● （4）优化合成报警视频质量，改善合成视频时丢帧问题
v4.406
● 发布时间 2024.04.18
● （1）优化报警视频合成模块，修复报警视频的不固定问题
● （2）新增支持设置报警封面帧在报警视频中的位置
● （3）改善包含报警视频的报警信息的触发延迟时间
● （4）修复gb28181接入视频流模块的sip信令中，客户端主动bye的资源释放问题
● （5）修复后台管理模块新增视频流不支持特殊字符的问题
● （6）修复部分linux系统修改视频分辨率花屏问题
v4.405
● 发布时间 2024.04.13
● （1）新增支持gb28181视频流接入协议
● （2）后台管理调整UI主题
● （3）启动配置文件新增支持，硬件编解码配置项
● （4）新增支持TensorRT推理yolo8-pose模型
● （5）新增支持pose模型的关键点渲染
v4.401
● 发布时间 2024.03.28
● （1）基础算法管理模块新增支持上传engine格式的模型
● （2）基础算法api类型扩展上传字段，扩展字段包括布控编号，算法编号，扩展字段，osd字段
● （3）行为算法api类型扩展上传字段，扩展字段包括布控编号，算法编号，扩展字段，osd字段
v4.40
● 发布时间 2024.03.24
● （1）分析器重构算法模块计算流程，重构后可支持不同算法模型的层级调用，并支持多种不同类型的算法框架
● （2）分析器新增支持检测算法>>追踪算法>>行为算法的新模式
● （3）分析器新增支持越线检测算法，可以用来检测目标是否逆行
● （4）后台管理重构布控页面的区域绘制功能，并新增越线检测绘制功能
● （5）后台管理重构上传基础算法模型的功能
v4.398
● 发布时间 2024.03.18
● （1）修复OSD参数的bug
● （2）修复英伟达多卡计算的bug
● （3）修复自定义API类型的基础算法接入的bug
● （4）新增支持可配置硬件解码路数和硬件编码路数（提升性能）
● （6）重构算法模块结构，重构后可支持算法模型的层级调用，并支持多种常见的算法层级调用流程
v4.394
● 发布时间 2024.02.29
● （1）扩展OSD参数，支持自定义任意位置的OSD贴图，并且支持中文内容的贴图
● （2）扩展布控参数，支持更多自定义功能
● （3）完善开放接口的字段校验功能
● （4）导入摄像头模板支持自定义摄像头编号
v4.391
● 发布时间 2024.02.19
● （1）修复后台管理sql语句bug
● （2）扩展报警数据的上传参数
● （3）分析器新增验证TensorRT模型是否正常的命令行功能
● （4）针对扩展算法检测模式，在原有自由竞争模式的基础上，扩展支持可配置间隔帧检测，扩展支持可配置间隔秒检测
● （5）修复二次开发报警接口测试功能
v4.39
● 发布时间 2024.02.16
● （1）基础算法模型实例化时支持自定义设置并发数量，提升硬件性能利用率
● （2）修复基础算法模型加密的bug
v4.37
● 发布时间 2024.02.06
● （1）基础算法推理支持 TensorRT-yolo5，TensorRT-yolo8，OpenVINO-yolo5，OpenVINO-yolo8，OpenVINO-ResNet
● （2）修复报警视频合成缓存清理bug
● （3）修复启动器控制bug
● （4）优化启动器调度性能
● （5）修复视频转码bug
v4.35
● 发布时间 2024.02.01
● （1）完善布控轮巡功能
● （2）布控轮巡功能支持分屏预览功能
● （3）修复此前版本配置模型缓存时长的bug
● （4）报警数据上传接口接入kafka，redis，mongodb
v4.34
● 发布时间 2024.01.28
● （1）新增支持配置项，模型缓存时长
● （2）新增支持配置项，重启时是否启用全部摄像头转发
● （3）新增支持布控轮巡功能
● （4）新增支持配置项，轮巡时是否启用自动管理转发（如果启用：轮巡布控时，自动开启摄像头转发，布控轮巡结束时，自动关闭摄像头转发）
● （5）新增支持布控分页功能
● （6）新增支持摄像头批量开启转发和关闭转发功能
● （7）新增支持通过复制批量为离线摄像头设置布控
● （8）修复模型加密功能的bug
v4.33
● 发布时间 2024.01.22
● （1）新增支持算法模型加密功能
● （2）新增支持火焰报警监测和烟雾报警监测功能
v4.32
● 发布时间 2024.01.13
● （1）新增支持加密锁授权
● （2）优化授权逻辑，修复此前机器授权码的bug
● （3）扩展系统前端自定义的功能范围
v4.31
● 发布时间 2024.01.10
● （1）新增支持报警数据上传接口功能
● （2）新增支持自定义系统名称，作者名称，作者链接，logo，title等功能
● （3）新增支持查询布控数据和查询视频流数据等开放接口功能
● （4）新增支持自定义合成报警视频时长，自定义报警视频延迟推送时长
v4.3
● 发布时间 2024.01.07
● （1）支持h265视频流播放
● （2）支持分屏功能，1分屏，4分屏，9分屏，16分屏，并支持全屏播放
● （3）支持在单屏或多分屏播放时，自适应转码视频分辨率，提升播放体验效果
● （4）优化后台管理功能逻辑
● （5）优化转码逻辑，提升性能
● （6）优化分析器调度逻辑
v4.25
● 发布时间 2023.12.30
● （1）优化算法实时处理推流的视频质量
● （2）基础算法模型新增可配置参数，可配置模型精度，预处理宽高，nms阈值，分类阈值
v4.24
● 发布时间 2023.12.28
● （1）解决报警延迟问题，已实现实时报警 
● （2）解决原报警视频合成方案导致的处理器和内存的过度消耗问题，大幅度提升软件性能
● （3）优化算法渲染推流，提高算法渲染推流流畅性
● （4）新增支持Ubuntu系统
v4.23
● 发布时间 2023.12.20
● （1）优化视频分析器的调度逻辑
● （2）新增支持英伟达显卡TensorRT推理。
v4.22
● 发布时间 2023.12.14
● （1）分析器在布控时新增推理设备是否支持的检测
● （2）分析器优化报警合成队列的使用机制
● （3）后台管理新增新版本检测功能，新版本弹窗提示功能
v4.21
● 发布时间 2023.12.13
● （1）优化解码和分析以及推流的队列内存复用
● （2）优化算法推流的流畅性
v4.2
● 发布时间 2023.12.10
● （1）解决系统稳定性问题，已经可以非常稳定的运行在配置比较一般的Windows设备
● （2）系统启动时新增环境检测功能，环境检测包括端口占用检测，程序重开检测，后续会增加处理器支持检测，显卡支持检测
v4.12
● 发布时间 2023.12.9
● （1）解决视频分析器在大规模布控情况下，布控稳定性问题
● （2）优化软件性能
v4.11
● 发布时间 2023.12.7
● （1）视频分析器优化布控调节，解决因为超量布控导致的程序崩溃
● （2）后台管理支持批量布控，布控复制，布控日志查询
v4.1
● 发布时间 2023.12.5
● （1）视频分析器新增支持API类型的基础算法接入
● （2）后台管理新增支持API类型的基础算法
● （3）后台管理基础算法支持设置布控数量上限
v4.0
● 发布时间 2023.12.3
● （1）视频分析服务优化合成报警视频的质量
● （2）视频分析服务优化因电脑性能不佳导致数据阻塞引起的程序崩溃（数据阻塞的原因在于消费速度小于生产速度）
● （3）视频分析服务支持动态模型实例化，动态模型删除，而不再是此前的指定启动模型实例，运行过程中不可删除，不可销毁
● （4）视频分析服务支持模型实例复用，多路布控共用同一个模型时，只开启一个模型实例
● （5）视频分析服务支持模型实例删除，多路布控共用同一个模型时，当该模型实例对应的所有布控都取消时，该模型实例也会取消并被删除
● （6）视频分析服务支持自动调节，在运行过程中可以根据资源的消耗情况，自动调节可支持的布控数量
● （7）视频分析服务支持无限次重试拉流，无限次重试推流
● （8）后台管理优化UI
● （9）后台管理支持自定义添加摄像头，批量导入摄像头，批量转发，自启动转发
● （10）后台管理支持自定义添加算法，包括基础算法和行为算法，对于基础算法，用户可以添加自己训练的模型，对于行为算法，可以选用系统内置的行为算法，也可以自己通过接口或动态库的方式二次开发
● （11）后台管理支持自定义添加报警声音，每一个布控东可以自定义报警音频
● （12）后台管理优化布控功能，新增报警视频类型的选项，报警图片数量的选项，布控目标的选项，阈值的选项。
● （13）后台管理优化报警查看功能，报警产生时页面自动刷新，并播放报警声音，新增报警详情页，可以进入报警详情页下载报警产生的视频和图片资料
● （14）扩展二次开发功能，用户可以基于本软件扩展算法，且不用考虑流媒体开发，音视频开发，编解码开发，后台管理开发等