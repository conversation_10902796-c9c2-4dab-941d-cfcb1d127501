功能模块,子模块,功能名称,功能描述,优先级,实现难度,依赖项
算法功能,基础算法支持,Yolo8-Seg分割算法,支持Yolo8-Seg分割算法和Yolo11-Seg分割算法,高,中,TensorRT/OpenVINO
算法功能,基础算法支持,Yolo8-obb算法,支持Yolo8-obb和Yolo11-obb算法模型,高,中,TensorRT/OpenVINO
算法功能,基础算法支持,Yolo8-pose模型,支持Yolo8-pose模型和关键点渲染,中,中,TensorRT
算法功能,基础算法支持,ResNet分类算法,支持ResNet分类算法,高,低,TensorRT/OpenVINO
算法功能,基础算法支持,XcFaceNet算法,支持XcFaceNet人脸特征提取算法,中,中,推理引擎
算法功能,基础算法支持,TensorRT推理引擎,支持TensorRT推理引擎（Yolo/ResNet/FaceNet/DeepSort等）,高,高,NVIDIA GPU
算法功能,基础算法支持,OpenVINO推理引擎,支持OpenVINO推理引擎,高,中,Intel硬件
算法功能,基础算法支持,OnnxRuntime推理引擎,支持OnnxRuntime推理引擎,中,低,ONNX模型
算法功能,基础算法支持,RKNPU推理引擎,支持RKNPU推理引擎（适用于RK芯片）,中,高,RK芯片
算法功能,基础算法支持,昇腾推理引擎,支持昇腾推理引擎,中,高,昇腾芯片
算法功能,行为算法功能,AREA/STAY/LEAVE算法,AREA/STAY/LEAVE行为算法（兼容分割算法）,高,中,基础检测算法
算法功能,行为算法功能,CROSSCOUNT算法,CROSSCOUNT行为算法（过滤相同ID重复触发）,高,中,目标追踪
算法功能,行为算法功能,SUPER行为算法,SUPER行为算法（支持配置目标任意位置作为计算中心点）,高,高,基础检测算法
算法功能,行为算法功能,越线检测算法,越线检测算法（检测目标逆行）,高,中,目标追踪
算法功能,行为算法功能,周界入侵算法,周界入侵行为算法,高,中,基础检测算法
算法功能,行为算法功能,打架行为算法,打架行为算法,中,高,姿态检测
算法功能,行为算法功能,暴力行为算法,暴力行为算法,中,高,姿态检测
算法功能,行为算法功能,目标计数算法,目标计数算法,高,中,基础检测算法
算法功能,行为算法功能,OVERLAP重叠算法,OVERLAP重叠后处理行为算法,中,中,基础检测算法
算法功能,行为算法功能,徘徊逗留算法,徘徊逗留后处理行为算法,高,中,目标追踪
算法功能,行为算法功能,无人值守算法,无人值守算法后处理,高,中,基础检测算法
算法功能,行为算法功能,离岗检测算法,离岗检测算法后处理,高,中,基础检测算法
算法功能,行为算法功能,COUNT计数算法,COUNT计数后处理行为算法,高,中,基础检测算法
算法功能,行为算法功能,火焰报警监测,火焰报警监测功能,中,中,基础检测算法
算法功能,行为算法功能,烟雾报警监测,烟雾报警监测功能,中,中,基础检测算法
算法功能,行为算法功能,PLATE行为算法,PLATE行为算法（车牌相关）,中,中,OCR算法
算法功能,行为算法功能,背景分类算法,背景分类算法（背景过滤）,低,中,分类算法
算法功能,行为算法功能,APIv2类型算法,APIv2类型行为算法（结合API和内置算法）,中,高,API接口
算法功能,视频质量检测,遮挡检测算法,遮挡检测算法,高,中,图像处理
算法功能,视频质量检测,灰屏检测算法,灰屏检测算法,高,低,图像处理
算法功能,视频质量检测,花屏检测算法,花屏检测算法,高,低,图像处理
算法功能,OCR识别算法,车牌识别算法,车牌识别算法,高,中,OCR引擎
算法功能,OCR识别算法,包装日期识别,包装日期识别算法,低,中,OCR引擎
算法功能,OCR识别算法,表单识别算法,表单识别算法,低,中,OCR引擎
算法功能,OCR识别算法,通用卡证识别,通用卡证识别算法,中,中,OCR引擎
算法功能,算法流程模式,模式1,检测算法>>行为算法,高,低,基础算法
算法功能,算法流程模式,模式2,检测算法>>追踪算法>>行为算法,高,中,追踪算法
算法功能,算法流程模式,模式3,检测算法>>分类算法>>行为算法,中,中,分类算法
算法功能,算法流程模式,模式4,分类算法>>行为算法,中,低,分类算法
算法功能,算法流程模式,模式5,行为算法,中,低,行为算法
算法功能,算法流程模式,模式6,分类算法->检测算法->行为算法,低,中,多算法组合
算法功能,算法流程模式,模式7,检测算法->分类算法->特征算法->行为算法,低,高,多算法组合
视频接入,视频流协议,GB28181协议,支持gb28181视频流接入协议,高,高,网络协议栈
视频接入,视频流协议,SSL加密协议,支持rtsps/rtmps/https等ssl加密协议,高,中,SSL证书
视频接入,视频流协议,H265播放,支持h265视频流播放,高,中,解码器
视频接入,视频流协议,MJpg播放,支持MJpg流播放,中,低,解码器
视频接入,视频流协议,音频接收,支持接收音频功能,中,中,音频解码
视频接入,视频流协议,被动推流,支持被动接收推流时自动新增对应数据,中,中,流媒体服务
视频接入,视频流协议,手动转发控制,支持手动开启转发和手动停止转发,高,低,流媒体控制
视频接入,GB28181功能,SIP注册兼容,SIP注册兼容多种注册信息加密算法,高,高,SIP协议
视频接入,GB28181功能,TCP模式,支持TCP模式和公网NAT网络环境,高,中,网络协议
视频接入,GB28181功能,多通道录像机,支持接入多通道录像机,中,中,设备兼容
视频接入,GB28181功能,云台控制,支持云台控制功能,中,中,设备控制
视频接入,GB28181功能,云台开放接口,提供云台开放接口,中,低,API接口
视频接入,视频解码功能,解码复用技术,支持视频解码复用技术,高,高,内存管理
视频接入,视频解码功能,硬件编解码,支持硬件解码和硬编码,高,高,硬件驱动
视频接入,视频解码功能,Intel集显解码,支持英特尔集显qsv硬解码,中,中,Intel驱动
视频接入,视频解码功能,跳帧解码,支持跳帧解码功能,中,中,解码优化
视频接入,视频解码功能,循环滤波控制,支持丢弃循环滤波和IDCT,低,中,解码优化
视频接入,视频解码功能,解码线程配置,支持可配置视频解码线程数量,中,低,线程管理
视频接入,视频解码功能,MPP硬解码,支持MPP视频硬解码加速（RK版）,中,高,RK硬件
视频接入,视频解码功能,RGA加速,支持RGA加速预处理（RK版）,中,高,RK硬件
视频接入,视频解码功能,编解码路数配置,支持可配置硬件解码路数和硬编码路数,中,中,资源管理
视频接入,视频流重试,拉流重试,拉流重试功能（中途中断视频流重试）,高,中,网络处理
视频接入,视频流重试,解码重试,解码重试功能（解码器故障重试）,高,中,错误处理
视频接入,视频流重试,无限重试,无限次重试拉流和推流,中,中,重试机制
视频接入,视频流重试,失败日志,连续读流/解码失败日志记录,高,低,日志系统
人脸识别,人脸检测识别,人脸识别功能,人脸识别功能（支持人脸图片质量过滤）,高,高,人脸算法
人脸识别,人脸检测识别,特征库初始化,人脸特征库初始化功能,高,中,数据库
人脸识别,人脸检测识别,陌生人报警,支持可配置陌生人报警,中,中,人脸比对
人脸识别,人脸检测识别,人脸行为算法,人脸识别行为算法,高,中,行为分析
人脸识别,人脸管理接口,添加删除人脸,添加人脸/删除人脸开放接口,高,低,API接口
人脸识别,人脸管理接口,查询人脸,查询所有人脸/根据图片查询人脸接口,高,中,数据库查询
人脸识别,人脸管理接口,算法控制,人脸算法开启/关闭接口,中,低,算法控制
语音识别,语音处理,音频解码,音频解码和音频重采样格式兼容,中,中,音频处理
语音识别,语音处理,内置推理引擎,内置推理引擎进行语音识别,中,高,语音算法
语音识别,语音处理,语音识别功能,语音识别功能（大规模重构分析器核心模块）,中,高,语音算法
录像存储,录像计划功能,录像计划,录像计划功能（可设置录像天数）,高,中,存储管理
录像存储,录像计划功能,录像回放,录像回放功能,高,中,视频播放
录像存储,录像计划功能,录音功能,录音功能支持,中,中,音频存储
录像存储,录像计划功能,手动录像,手动录像功能,中,低,用户操作
录像存储,录像计划功能,手动抓拍,手动抓拍图片功能,中,低,用户操作
录像存储,录像管理接口,录像计划管理,添加录像计划/删除录像计划接口,高,低,API接口
录像存储,录像管理接口,录像查询,查询录像计划接口,高,低,数据库查询
录像存储,录像管理接口,文件列表查询,查询录像文件列表接口,高,中,文件系统
录像存储,录像管理接口,播放地址查询,查询录像文件播放地址接口,高,低,URL生成
录像存储,存储管理,自动覆盖,报警数据和录像数据按存储空间上限自动覆盖,高,中,存储策略
录像存储,存储管理,存储路径配置,可配置存储根路径,中,低,配置管理
录像存储,存储管理,自动清理,自动定时清理日志和缓存功能,高,中,定时任务
录像存储,存储管理,手动清理,手动清理日志缓存功能,中,低,用户操作
录像存储,存储管理,NVR存储,NVR存储功能和播放功能,中,中,NVR协议
报警管理,报警处理,实时报警,实时报警功能,高,中,实时处理
报警管理,报警处理,视频合成,报警视频合成功能,高,高,视频处理
报警管理,报警处理,图片合成,报警图片合成（支持画框/不画框/画框+不画框三种类型）,高,中,图像处理
报警管理,报警处理,报警过滤器,报警过滤器功能,中,中,规则引擎
报警管理,报警处理,强制触发,强制触发报警功能,中,低,算法控制
报警管理,报警处理,邮件通知,邮件通知功能,中,低,邮件服务
报警管理,报警处理,报警审核,报警审核功能,中,中,工作流
报警管理,报警数据管理,数据查询删除,报警数据查询/删除/清理缓存接口,高,中,数据库操作
报警管理,报警数据管理,样本导出,报警数据导出为样本接口,中,中,数据导出
报警管理,报警数据管理,数据上传,报警数据上传接口（支持kafka，redis，mongodb）,高,高,消息队列
报警管理,报警数据管理,视频分组字段,报警数据上传支持视频分组字段,中,低,数据结构
报警管理,报警数据管理,URL字段支持,报警数据上传支持图片或视频url字段（替代base64）,中,中,数据格式
报警管理,报警数据管理,Base64配置,可配置关闭base64字段的上传,低,低,配置选项
报警管理,报警数据管理,自动清理配置,可配置自动清理报警数据（设置保存天数）,高,中,存储策略
报警管理,报警数据管理,JSON描述文件,写入本地报警数据包含描述结果的json文件,中,低,文件格式
报警管理,报警视频管理,筛选条件,报警视频管理页面筛选条件（布控、视频流、算法、日期范围）,高,中,查询功能
报警管理,报警视频管理,报警详情页,报警详情页面（下载报警视频和图片资料）,高,中,用户界面
报警管理,报警视频管理,自动刷新,报警产生时页面自动刷新并播放报警声音,中,中,实时更新
报警管理,报警视频管理,不画框数据显示,显示不画框的报警数据,中,低,数据展示
设备管理,摄像头管理,自定义添加,自定义添加摄像头,高,低,设备管理
设备管理,摄像头管理,批量导入,批量导入摄像头,高,中,批量操作
设备管理,摄像头管理,批量转发,批量转发功能,中,中,批量操作
设备管理,摄像头管理,自启动转发,自启动转发功能,中,中,自动化
设备管理,摄像头管理,ONVIF搜索,ONVIF搜索功能,中,中,设备发现
设备管理,摄像头管理,ONVIF地址获取,ONVIF自动获取地址功能,中,中,设备配置
设备管理,摄像头管理,ONVIF设备信息,ONVIF获取设备信息功能,中,中,设备查询
设备管理,摄像头管理,ONVIF截屏,ONVIF截屏功能,低,中,设备操作
设备管理,摄像头管理,全部删除,摄像头全部删除功能,中,低,批量操作
设备管理,摄像头管理,自定义编号,自定义摄像头编号,中,低,配置管理
设备管理,摄像头管理,模板导入编号,导入摄像头模板支持自定义编号,中,低,模板功能
设备管理,摄像头管理,分组搜索,视频管理页面分组和搜索功能,高,中,查询功能
设备管理,布控管理,自定义添加,自定义添加布控,高,中,布控配置
设备管理,布控管理,批量布控,批量布控功能,高,中,批量操作
设备管理,布控管理,布控复制,布控复制功能,中,低,配置复制
设备管理,布控管理,布控轮巡,布控轮巡功能,中,高,任务调度
设备管理,布控管理,布控分页,布控分页功能,中,低,分页显示
设备管理,布控管理,快捷设置,快捷设置功能,高,中,用户体验
设备管理,布控管理,多进程支持,多进程模式支持,高,高,进程管理
设备管理,布控管理,日志查询,布控日志查询,中,中,日志系统
设备管理,布控管理,数量上限,布控数量上限设置,中,低,资源限制
设备管理,布控管理,离线布控复制,通过复制批量为离线摄像头设置布控,中,中,离线处理
设备管理,布控管理,目标配置,布控目标选项配置,高,中,配置管理
设备管理,布控管理,阈值配置,阈值选项配置,高,中,参数配置
设备管理,布控管理,分组搜索功能,布控管理页面分组和搜索功能,高,中,查询功能
设备管理,布控管理,轮巡分屏预览,布控轮巡分屏预览功能,中,中,视频显示
设备管理,布控管理,自动转发管理,轮巡时自动管理转发功能,中,中,自动化
系统集成,集群管理,xcnvs平台接入,接入xcnvs集群管理平台,中,高,第三方集成
系统集成,集群管理,节点管理,支持查看和管理任意节点的摄像头/算法流/录像回放,中,高,分布式管理
系统集成,集群管理,公网部署,适合公网部署管理分散内网节点,中,高,网络架构
系统集成,多进程支持,负载均衡,高级版多进程负载均衡算法,高,高,负载均衡
系统集成,多进程支持,自动均衡,自动负载均衡模式,高,高,自动化
系统集成,多进程支持,数据同步,多进程环境下数据同步,高,高,数据一致性
系统集成,多进程支持,实时信息显示,多进程实时信息显示,中,中,监控界面
系统集成,多进程支持,进程归属显示,业务算法所属进程显示,中,低,信息展示
系统集成,多进程支持,自动恢复,异常停止布控自动恢复,高,高,故障恢复
开放接口,基础接口,摄像头接口,添加摄像头/删除摄像头接口,高,低,API接口
开放接口,基础接口,系统信息查询,查询软件基本信息接口,中,低,系统查询
开放接口,基础接口,存储信息查询,查询存储信息接口,中,低,存储查询
开放接口,基础接口,系统控制,重启软件/重启系统接口,中,中,系统控制
开放接口,基础接口,手动操作,手动抓拍图片和手动录像接口,中,低,用户操作
开放接口,基础接口,版本检测,检测版本接口（上传推理引擎参数）,低,低,版本管理
开放接口,测试验证接口,算法模型测试,测试基础算法模型的工具,中,中,测试工具
开放接口,测试验证接口,TensorRT验证,验证TensorRT模型是否正常的命令行功能,中,中,模型验证
开放接口,测试验证接口,报警接口测试,二次开发报警接口测试功能,中,中,接口测试
开放接口,算法接口,图片检测,图片检测接口,高,中,算法调用
开放接口,算法接口,API算法接入,API类型基础算法接入,高,高,算法集成
开放接口,算法接口,行为算法扩展,API行为算法扩展,中,高,算法扩展
开放接口,算法接口,算法开放能力,算法开放能力接口,中,高,能力开放
开放接口,安全验证,接口安全校验,开放接口安全校验机制,高,中,安全认证
开放接口,安全验证,模块间验证,模块间安全验证逻辑,高,中,内部安全
用户管理,用户权限,用户管理模块,用户管理模块,高,中,权限系统
用户管理,用户权限,权限区分,区分管理员和普通用户,高,中,角色管理
用户管理,用户权限,用户操作,管理员可以增删改查用户,高,低,用户操作
用户管理,登录安全,加密传输,账号信息公钥加密传输,高,中,加密技术
用户管理,登录安全,验证码,验证码功能,中,低,验证机制
用户管理,登录安全,登录锁定,连续登录失败锁定功能,高,中,安全策略
用户管理,登录安全,白名单,HOST白名单设置,中,低,访问控制
用户管理,系统自定义,系统名称,自定义系统名称、作者名称、作者链接,低,低,界面定制
用户管理,系统自定义,界面定制,自定义logo、title等,低,低,界面定制
用户管理,系统自定义,配置参数,自定义配置参数（settings.json）,中,低,配置管理
用户管理,系统自定义,地址配置,修改下载地址、文档地址等,低,低,配置管理
用户管理,系统自定义,前端自定义,扩展系统前端自定义功能范围,低,中,前端扩展
任务计划,定时任务,任务计划功能,任务计划功能（定时执行布控任务）,中,中,任务调度
任务计划,定时任务,视频流转发,定时执行视频流转发任务,中,中,任务调度
任务计划,定时任务,轮巡任务,定时轮巡任务,中,中,任务调度
任务计划,定时任务,系统重启,定时重启软件/系统,中,中,系统控制
任务计划,定时任务,设备扫描,间隔扫描离线摄像头,中,中,设备监控
任务计划,计划类型,人脸识别,自动开启人脸识别类型,中,中,算法控制
任务计划,计划类型,参数配置,可配置计划任务参数,中,低,配置管理
显示渲染,视频播放,分屏功能,分屏功能（1分屏，4分屏，9分屏，16分屏）,高,中,视频显示
显示渲染,视频播放,全屏播放,全屏播放功能,中,低,视频显示
显示渲染,视频播放,自适应转码,自适应转码视频分辨率,中,中,视频处理
显示渲染,视频播放,Web播放器,h265转码器方案替代为web播放器,中,高,播放技术
显示渲染,算法渲染,动态编码,算法流动态编码功能,中,高,视频编码
显示渲染,算法渲染,中文名称,绘制中文名称支持,中,中,文字渲染
显示渲染,算法渲染,多目标框,绘制多目标框（矩形、多边形）,高,中,图形渲染
显示渲染,算法渲染,自定义样式,自定义颜色/厚度/字体大小,中,中,样式配置
显示渲染,算法渲染,OSD贴图,OSD贴图功能（支持中文内容）,中,中,图像叠加
显示渲染,算法渲染,自定义渲染,用户自定义渲染目标,中,高,渲染扩展
显示渲染,算法渲染,坐标设置,算法和FPS显示起点坐标设置,低,低,界面配置
显示渲染,算法渲染,线段样式,绘制线段自定义样式,中,低,图形渲染
显示渲染,算法渲染,关键点渲染,关键点渲染支持（pose模型）,中,中,特殊渲染
配置管理,配置导入导出,配置导入导出,在线导出配置/导入配置功能,中,中,配置管理
配置管理,配置导入导出,日志导出,在线导出日志功能,中,中,日志管理
配置管理,配置导入导出,配置文件导出,导出日志包含config.json,settings.json,config.ini,中,低,文件导出
配置管理,配置导入导出,数据导出,导出在线视频流和布控数据,中,中,数据导出
配置管理,配置导入导出,流媒体日志,可配置导出流媒体日志,低,低,日志配置
配置管理,文件服务,硬盘位置,文件服务可指向任意硬盘位置,中,中,文件系统
配置管理,文件服务,HTTP访问,对外提供HTTP访问,中,中,Web服务
配置管理,文件服务,存储配置,报警数据存储配置在任意硬盘位置,中,中,存储配置
配置管理,参数配置,性能参数,可配置ps_effect_min_fps和pull_frequency,中,中,性能调优
配置管理,参数配置,中文提示词,可配置中文提示词,低,低,本地化
配置管理,参数配置,IP音柱参数,可配置IP音柱报警参数,低,中,设备集成
配置管理,参数配置,多进程配置,可配置多进程模式,高,中,进程管理
配置管理,参数配置,调试模式,开启调试模式功能,中,低,调试工具
配置管理,参数配置,启动配置,软件启动配置修改,中,低,启动管理
授权安全,授权管理,机器授权码,机器授权码授权,高,中,授权验证
授权安全,授权管理,加密锁授权,加密锁授权,中,中,硬件授权
授权安全,授权管理,设备ID绑定,系统设备ID绑定授权,中,中,设备绑定
授权安全,授权管理,永久授权,永久授权码支持,中,低,授权管理
授权安全,安全功能,模型加密,算法模型加密功能,中,高,数据加密
授权安全,安全功能,访问权限限制,限制软件子模块服务对外访问权限,高,中,安全策略
授权安全,安全功能,参数加密,限制打印模型加密扩展参数,中,中,信息安全
性能优化,硬件加速,TensorRT优化,TensorRT推理预处理优化,高,高,算法优化
性能优化,硬件加速,多卡支持,英伟达显卡多卡计算支持,中,高,硬件支持
性能优化,硬件加速,ARM加速,ARM架构硬件加速（RK、昇腾、Jetson）,中,高,硬件适配
性能优化,硬件加速,MPP加速,MPP视频硬解码加速,中,高,硬件加速
性能优化,硬件加速,RGA加速,RGA加速支持,中,高,硬件加速
性能优化,硬件加速,Neon指令集,Neon指令集加速（ARM版本）,中,中,指令优化
性能优化,硬件加速,Xcc兼容库,Xcc硬件加速兼容库,低,中,硬件兼容
性能优化,算法优化,决策逻辑,算法决策逻辑优化,中,中,算法优化
性能优化,算法优化,模型复用,模型实例复用技术,高,高,资源优化
性能优化,算法优化,动态实例化,动态模型实例化和删除,高,高,资源管理
性能优化,算法优化,自动调节,自动调节布控数量,中,高,自适应
性能优化,算法优化,内存复用,内存复用技术,高,高,内存优化
性能优化,算法优化,解码复用,解码内存复用技术,高,高,解码优化
性能优化,算法优化,预处理优化,预处理性能优化,中,中,算法优化
性能优化,系统优化,负载均衡,自动负载均衡模式,高,高,系统架构
性能优化,系统优化,缓存配置,模型缓存时长配置,中,低,缓存策略
性能优化,系统优化,间隔检测,间隔帧检测配置,中,中,检测策略
性能优化,系统优化,间隔秒检测,间隔秒检测配置,中,中,检测策略
性能优化,系统优化,竞争模式,自由竞争模式扩展,低,中,调度策略
系统环境,操作系统,Windows支持,Windows平台支持,高,低,系统兼容
系统环境,操作系统,Ubuntu支持,Ubuntu系统支持,高,中,系统兼容
系统环境,操作系统,Linux支持,Linux系统支持,高,中,系统兼容
系统环境,环境检测,端口检测,端口占用检测,中,低,环境检查
系统环境,环境检测,程序检测,程序重开检测,中,低,进程检查
系统环境,环境检测,设备检测,推理设备支持检测,中,中,硬件检查
系统环境,环境检测,处理器检测,处理器支持检测,中,中,硬件检查
系统环境,环境检测,显卡检测,显卡支持检测,中,中,硬件检查
系统环境,软件启动,Windows自启动,Windows平台软件自启动,中,低,系统集成
系统环境,软件启动,启动配置,软件启动配置修改,中,低,配置管理
系统环境,软件启动,检测优化,环境检测执行速度优化,低,中,性能优化
系统环境,软件启动,启动器优化,启动器控制逻辑优化,中,中,启动管理
模型管理,模型格式,Engine格式,engine格式模型上传,中,中,模型支持
模型管理,模型格式,RKNN模型,rknn模型支持,中,高,模型支持
模型管理,模型格式,OM模型,om模型支持,中,高,模型支持
模型管理,模型格式,TensorRT验证,TensorRT模型验证,中,中,模型验证
模型管理,模型加密,算法加密,算法模型加密,中,高,数据安全
模型管理,模型加密,试用时长,模型试用时长设置,中,中,授权管理
模型管理,模型加密,自定义编号,自定义编号支持,低,低,标识管理
模型管理,模型加密,自动加密,自动加密判断,中,中,自动化
模型管理,模型实例,动态实例化,动态模型实例化,高,高,资源管理
模型管理,模型实例,实例删除,模型实例删除,高,中,资源清理
模型管理,模型实例,实例复用,模型实例复用,高,高,资源优化
模型管理,模型实例,并发设置,自定义并发数量设置,中,中,性能配置
模型管理,模型实例,自适应分辨率,自适应模型分辨率,中,中,模型适配
数据导出,样本导出,Labelme格式,报警图片导出为labelme格式训练样本,中,中,数据格式
数据导出,样本导出,不画框导出,不画框类型报警图片导出,中,低,数据处理
数据导出,样本导出,样本管理,报警数据导出为样本,中,中,数据管理
数据导出,配置导出,配置导入导出,在线导出配置/导入配置,中,中,配置管理
数据导出,配置导出,配置文件,导出config.json,settings.json,config.ini,中,低,文件管理
数据导出,配置导出,流布控数据,导出在线视频流和布控数据,中,中,数据导出
数据导出,配置导出,流媒体日志,可配置导出流媒体日志,低,低,日志管理
版本管理,版本检测,版本检测功能,新版本检测功能,中,低,版本管理
版本管理,版本检测,弹窗提示,新版本弹窗提示,低,低,用户提醒
版本管理,版本检测,升级包控制,版本升级包控制,中,中,升级管理
版本管理,离线升级,离线升级包,导入离线升级包更新版本,中,中,离线部署
版本管理,离线升级,兼容性控制,升级包版本兼容性控制,中,中,版本兼容
版本管理,离线升级,版本验证,最低版本要求验证,中,低,版本检查
二次开发,算法扩展,动态库算法,动态库类型行为算法,中,高,扩展开发
二次开发,算法扩展,兼容库,兼容算法动态库,中,高,硬件兼容
二次开发,算法扩展,国产硬件,国产硬件算法兼容,中,高,硬件适配
二次开发,算法扩展,自定义训练,用户自定义算法训练,中,高,算法定制
二次开发,开发接口,检测模式扩展,扩展算法检测模式,中,高,模式扩展
二次开发,开发接口,报警接口,二次开发报警接口,中,高,接口开发
二次开发,开发接口,能力接口,算法开放能力接口,中,高,能力开放
二次开发,开发接口,开发简化,流媒体开发免除,中,高,开发便利
